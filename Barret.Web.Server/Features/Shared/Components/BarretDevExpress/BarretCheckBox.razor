@using Radzen.Blazor

<RadzenCheckBox @bind-Value="@Checked"
               Text="@Text"
               ReadOnly="@ReadOnly"
               Disabled="@(!Enabled)"
               class="@($"barret-input barret-form-checkbox {CssClass}")">
</RadzenCheckBox>

@code {
    /// <summary>
    /// Gets or sets whether the checkbox is checked.
    /// </summary>
    [Parameter]
    public bool Checked { get; set; }

    /// <summary>
    /// Event callback for when the checked state changes.
    /// </summary>
    [Parameter]
    public EventCallback<bool> CheckedChanged { get; set; }

    /// <summary>
    /// Gets or sets the text to display next to the checkbox.
    /// </summary>
    [Parameter]
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the checkbox is read-only.
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Gets or sets whether the checkbox is enabled.
    /// </summary>
    [Parameter]
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the CSS class.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;
}
