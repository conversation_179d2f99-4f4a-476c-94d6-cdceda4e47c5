@using Radzen.Blazor
@using DevExpress.Blazor

<RadzenNumeric Value="@((decimal)Value)"
              ValueChanged="@((decimal value) => OnValueChanged((double)value))"
              Placeholder="@Placeholder"
              ReadOnly="@ReadOnly"
              Disabled="@(!Enabled)"
              ShowUpDown="true"
              Min="@(MinValue == double.MinValue ? null : (decimal?)MinValue)"
              Max="@(MaxValue == double.MaxValue ? null : (decimal?)MaxValue)"
              Step="@Step.ToString()"
              class="@($"barret-input {GetSizeClass()} {CssClass}")">
</RadzenNumeric>

@code {
    /// <summary>
    /// Gets or sets the value.
    /// </summary>
    [Parameter]
    public double Value { get; set; }

    /// <summary>
    /// Event callback for when the value changes.
    /// </summary>
    [Parameter]
    public EventCallback<double> ValueChanged { get; set; }

    /// <summary>
    /// Gets or sets the placeholder text.
    /// </summary>
    [Parameter]
    public string Placeholder { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the number box is read-only.
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Gets or sets whether the number box is enabled.
    /// </summary>
    [Parameter]
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the CSS class.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the clear button display mode.
    /// </summary>
    [Parameter]
    public DataEditorClearButtonDisplayMode ClearButtonDisplayMode { get; set; } = DataEditorClearButtonDisplayMode.Auto;

    /// <summary>
    /// Gets or sets the size mode.
    /// </summary>
    [Parameter]
    public SizeMode SizeMode { get; set; } = SizeMode.Medium;

    /// <summary>
    /// Gets or sets the minimum value.
    /// </summary>
    [Parameter]
    public double MinValue { get; set; } = double.MinValue;

    /// <summary>
    /// Gets or sets the maximum value.
    /// </summary>
    [Parameter]
    public double MaxValue { get; set; } = double.MaxValue;

    /// <summary>
    /// Gets or sets the step value.
    /// </summary>
    [Parameter]
    public double Step { get; set; } = 1;

    /// <summary>
    /// Handles the value changed event.
    /// </summary>
    /// <param name="newValue">The new value.</param>
    private async Task OnValueChanged(double newValue)
    {
        if (Value != newValue)
        {
            Value = newValue;
            if (ValueChanged.HasDelegate)
            {
                await ValueChanged.InvokeAsync(newValue);
            }
        }
    }

    /// <summary>
    /// Gets the appropriate CSS size class based on SizeMode.
    /// </summary>
    /// <returns>The CSS class for the current size mode.</returns>
    private string GetSizeClass()
    {
        return SizeMode switch
        {
            SizeMode.Small => "barret-form-input-sm",
            SizeMode.Large => "barret-form-input-lg",
            _ => "barret-form-input"
        };
    }
}
