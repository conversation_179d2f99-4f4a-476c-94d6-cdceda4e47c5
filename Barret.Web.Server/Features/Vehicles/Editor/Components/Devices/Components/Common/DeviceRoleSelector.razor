@using Barret.Core.Areas.Devices.Enums
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@using DevExpress.Blazor
@using Radzen.Blazor
@inherits DeviceRoleSelectorBase

<DxPopup @bind-Visible="@IsVisible"
         HeaderText="Select Device Type"
         ShowFooter="true"
         Width="600px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         ShowCloseButton="true"
         CssClass="device-role-selector-popup accessible-popup"
         TabIndex="0">
    <HeaderTemplate>
        <div class="d-flex align-items-center">
            <i class="bi bi-device-hdd me-2"></i>
            <span>Select Device Type</span>
        </div>
    </HeaderTemplate>
    <ChildContent>
        <div class="device-role-selector-container p-3">
            <div class="mb-3">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <RadzenTextBox Placeholder="Search device types..."
                                  Value="@SearchText"
                                  ValueChanged="@((value) => OnSearchTextChanged(value))"
                                  class="barret-input barret-form-input" />
                </div>
            </div>

            <div class="device-role-grid">
                @if (FilteredRoles.Count == 0)
                {
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        No device types found matching your search.
                    </div>
                }
                else
                {
                    <div class="row row-cols-2 g-3">
                        @foreach (var roleViewModel in FilteredRoles)
                        {
                            <div class="col">
                                <div class="device-role-card" @onclick="() => SelectRoleAsync(roleViewModel.Role)">
                                    <div class="device-role-icon">
                                        <i class="@roleViewModel.IconCssClass"></i>
                                    </div>
                                    <div class="device-role-name">
                                        @roleViewModel.DisplayName
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
            </div>
        </div>
    </ChildContent>
    <FooterTemplate>
        <div class="barret-btn-group justify-end p-3">
            <RadzenButton Text="Cancel"
                         Icon="cancel"
                         ButtonStyle="ButtonStyle.Light"
                         Click="@CancelAsync"
                         class="barret-btn barret-form-btn" />
        </div>
    </FooterTemplate>
</DxPopup>
