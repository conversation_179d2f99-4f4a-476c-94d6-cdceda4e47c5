@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Vehicles.Vessels
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@using Radzen.Blazor
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Import.ViewModels
@inherits DeviceImportDialogViewBase



<DxPopup @bind-Visible="@Visible"
         HeaderText="Import Devices"
         ShowFooter="true"
         Width="900px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         ShowCloseButton="true"
         Closing="@HandleClosing">
    <HeaderTemplate>
        <div class="d-flex align-items-center">
            <i class="bi bi-download me-2"></i>
            <span>Import Devices</span>
        </div>
    </HeaderTemplate>
    <ChildContent>
        <div class="p-3">
            @if (ViewModel.IsLoading)
            {
                <div class="d-flex justify-content-center my-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else
            {
                <div class="mb-3">
                    <h5>Available Vessels and Devices</h5>
                    <p class="text-muted">Expand a vessel to view its devices and select the ones you want to import.</p>
                </div>

                <RadzenDataGrid @ref="VesselGrid"
                               Data="@ViewModel.Vessels"
                               TItem="VesselWithDevicesViewModel"
                               Class="barret-grid device-import-grid mb-4"
                               AllowFiltering="true"
                               AllowPaging="true"
                               PageSize="10"
                               RowSelect="@OnVesselRowSelect"
                               ExpandMode="DataGridExpandMode.Single">
                    <Columns>
                        <RadzenDataGridColumn TItem="VesselWithDevicesViewModel" Title="Name" Sortable="true" Filterable="true">
                            <Template Context="vessel">
                                @{
                                    var vesselViewModel = vessel as VesselWithDevicesViewModel;
                                    if (vesselViewModel != null)
                                    {
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-ship me-2"></i>
                                            <span class="fw-bold">@vesselViewModel.Vessel.Name</span>
                                            <span class="text-muted ms-2">(@vesselViewModel.Vessel.VehicleId)</span>
                                        </div>
                                    }
                                }
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn TItem="VesselWithDevicesViewModel" Title="Devices" Width="120px" Sortable="false" Filterable="false">
                            <Template Context="vessel">
                                @{
                                    var vesselViewModel = vessel as VesselWithDevicesViewModel;
                                    if (vesselViewModel != null)
                                    {
                                        <div class="d-flex align-items-center">
                                            @if (vesselViewModel.IsLoaded)
                                            {
                                                <span class="badge bg-info">@vesselViewModel.Devices.Count</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">-</span>
                                            }
                                        </div>
                                    }
                                }
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn TItem="VesselWithDevicesViewModel" Title="Select All" Width="120px" Sortable="false" Filterable="false">
                            <Template Context="vessel">
                                @{
                                    var vesselViewModel = vessel as VesselWithDevicesViewModel;
                                    if (vesselViewModel != null && vesselViewModel.IsLoaded && vesselViewModel.Devices.Count > 0)
                                    {
                                        <BarretCheckBox Checked="@AreAllDevicesSelectedInVessel(vesselViewModel)"
                                                      CheckedChanged="@((value) => { ToggleAllDevicesInVessel(vesselViewModel, value); StateHasChanged(); })" />
                                    }
                                }
                            </Template>
                        </RadzenDataGridColumn>
                    </Columns>
                    <Template Context="vessel">
                        @{
                            var vesselViewModel = vessel as VesselWithDevicesViewModel;
                            if (vesselViewModel != null)
                            {
                                if (!vesselViewModel.IsLoaded)
                                {
                                    <div class="d-flex justify-content-center my-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                }
                                else if (vesselViewModel.Devices.Count == 0)
                                {
                                    <div class="p-3">
                                        <div class="alert alert-info mb-0">
                                            <i class="bi bi-info-circle me-2"></i>
                                            No devices found in this vessel.
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <RadzenDataGrid @ref="DeviceGrid"
                                                   Data="@vesselViewModel.Devices"
                                                   TItem="DeviceDto"
                                                   Class="barret-grid device-list-grid mb-0"
                                                   AllowFiltering="true"
                                                   AllowPaging="true"
                                                   PageSize="5"
                                                   RowSelect="@OnDeviceRowSelect">
                                        <Columns>
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="" Width="50px" Sortable="false" Filterable="false">
                                                <Template Context="device">
                                                    @{
                                                        var deviceDto = device as DeviceDto;
                                                        if (deviceDto != null)
                                                        {
                                                            <BarretCheckBox Checked="@IsDeviceSelected(deviceDto.Id)"
                                                                          CheckedChanged="@((value) => { ToggleDeviceSelection(deviceDto.Id); StateHasChanged(); })" />
                                                        }
                                                    }
                                                </Template>
                                            </RadzenDataGridColumn>
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Name" Property="Name" Sortable="true" Filterable="true">
                                                <Template Context="device">
                                                    @{
                                                        var deviceDto = device as DeviceDto;
                                                        if (deviceDto != null)
                                                        {
                                                            <span class="@(IsDeviceSelected(deviceDto.Id) ? "fw-bold text-primary" : "")">@deviceDto.Name</span>
                                                        }
                                                    }
                                                </Template>
                                            </RadzenDataGridColumn>
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Role" Property="DeviceRole" Sortable="true" Filterable="true" />
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Manufacturer" Property="ManufacturerName" Sortable="true" Filterable="true" />
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Model" Property="ModelName" Sortable="true" Filterable="true" />
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Group" Property="DeviceGroupName" Sortable="true" Filterable="true" />
                                        </Columns>
                                    </RadzenDataGrid>
                                }
                            }
                        }
                    </Template>
                </RadzenDataGrid>

                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-primary">@ViewModel.GetSelectedDeviceCount() devices selected</span>
                    </div>
                </div>
            }
        </div>
    </ChildContent>
    <FooterTemplate>
        <div class="d-flex justify-content-end">
            <BarretActionButton Text="Cancel"
                              RenderStyle="ButtonRenderStyle.Secondary"
                              OnClick="@CloseDialog"
                              CssClass="me-2" />
            <BarretActionButton Text="Import Selected Devices"
                              RenderStyle="ButtonRenderStyle.Primary"
                              OnClick="@ImportDevices"
                              IconCssClass="bi bi-download"
                              Enabled="@(ViewModel.GetSelectedDeviceCount() > 0)" />
        </div>
    </FooterTemplate>
</DxPopup>
