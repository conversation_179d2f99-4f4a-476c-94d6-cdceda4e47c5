Phase 3C Data Grid Component Migration Inventory
===============================================

Generated: $(date)
Total DxGrid Components Found: 12 instances across 10 files

COMPONENT BREAKDOWN:
===================

Direct DxGrid Usage: 9 instances
- ConnectionManager.razor: 1 instance (line 51)
- DeviceManager.razor: 1 instance (line 88) - Complex grid with multiple columns
- InterfaceManager.razor: 1 instance (line 45)
- ManufacturersManagerView.razor: 1 instance (line 62)
- DeviceModelsManagerView.razor: 1 instance (line 60)
- DeviceConnectionsPanel.razor: 2 instances (lines 107, 171) - Connection grids
- DeviceGroupTabView.razor: 1 instance (line 72) - Device listing grid
- DeviceImportDialogView.razor: 2 instances (lines 42, 122) - Import dialog grids

Wrapper Components: 3 instances
- BarretDataGrid.razor: 1 instance (line 5) - Generic wrapper
- BarretDeviceGrid.razor: 1 instance (line 6) - Device-specific wrapper
- BarretVehicleGrid.razor: 1 instance (line 17) - Vehicle-specific wrapper

MIGRATION PRIORITY ORDER (by complexity):
=========================================

1. Wrapper Components (3 instances) - Foundation components
   - BarretDataGrid.razor - Generic grid wrapper
   - BarretDeviceGrid.razor - Device grid wrapper  
   - BarretVehicleGrid.razor - Vehicle grid wrapper

2. Simple Grids (4 instances) - Basic data display
   - ConnectionManager.razor - Connection listing
   - InterfaceManager.razor - Interface connections
   - ManufacturersManagerView.razor - Manufacturer management
   - DeviceModelsManagerView.razor - Device model management

3. Complex Grids (5 instances) - Advanced functionality
   - DeviceManager.razor - Complex device grid with actions
   - DeviceConnectionsPanel.razor (2 grids) - Connection management
   - DeviceGroupTabView.razor - Device group management
   - DeviceImportDialogView.razor (2 grids) - Import functionality

GRID FEATURES ANALYSIS:
======================

Common Features:
- Data binding (@Data property)
- Column definitions (DxGridDataColumn)
- Template columns with custom content
- Action columns with buttons
- Field-based columns (FieldName property)
- Width specifications
- Caption/header text

Advanced Features:
- Selection columns (DxGridSelectionColumn)
- Summary items (DxGridSummaryItem)
- Custom templates in columns
- Row selection and events
- Nested grids (DeviceImportDialogView)
- Complex data binding patterns

FILES TO MIGRATE:
================

Wrapper Components:
- ./Features/Shared/Components/BarretDevExpress/BarretDataGrid.razor
- ./Features/Shared/Components/BarretDevExpress/BarretDeviceGrid.razor
- ./Features/Shared/Components/BarretDevExpress/BarretVehicleGrid.razor

Direct Usage Components:
- ./Shared/Components/DeviceManagers/ConnectionManager.razor
- ./Shared/Components/DeviceManagers/DeviceManager.razor
- ./Shared/Components/DeviceManagers/InterfaceManager.razor
- ./Features/Admin/Components/ManufacturersManagerView.razor
- ./Features/Admin/Components/DeviceModelsManagerView.razor
- ./Features/Vehicles/Editor/Components/Devices/Components/DeviceConnectionsPanel.razor
- ./Features/Vehicles/Editor/Components/Tabs/DeviceGroupTabView.razor
- ./Features/Vehicles/Editor/Components/Import/Views/DeviceImportDialogView.razor

SPECIAL CONSIDERATIONS:
======================

1. DeviceImportDialogView.razor has nested grids requiring careful handling
2. DeviceManager.razor has complex action columns with multiple buttons
3. Selection functionality in DeviceImportDialogView needs special attention
4. Summary items (DxGridSummaryItem) need RadzenDataGrid equivalent
5. Template columns with custom Blazor content need proper conversion
6. Grid references (@ref="Grid") need to be updated for RadzenDataGrid

ESTIMATED COMPLEXITY:
====================
- Low: Wrapper components, simple data grids (7 instances)
- Medium: Grids with action columns (3 instances) 
- High: Nested grids, selection grids (2 instances)

Total: 12 DxGrid instances requiring migration to RadzenDataGrid
