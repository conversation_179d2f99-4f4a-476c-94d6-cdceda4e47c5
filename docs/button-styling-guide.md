# Barret Button Styling Guide

## Overview

This guide provides a centralized styling system for RadzenButton components across the Barret Vehicle Configurator application. The system uses Tailwind CSS utility classes to ensure consistent button styling and spacing throughout the application.

## Centralized Button Classes

### Base Button Class

All buttons should include the base `barret-btn` class for consistent transitions and typography:

```razor
<RadzenButton Text="Example" 
              ButtonStyle="ButtonStyle.Primary"
              class="barret-btn barret-form-btn" />
```

### Button Sizing Classes

Use these classes instead of manual padding for consistent sizing:

#### Standard Sizes
- **`barret-btn-sm`**: Small buttons (`px-3 py-1.5 text-sm`)
- **`barret-btn-md`**: Medium buttons (`px-4 py-2 text-base`) - Default
- **`barret-btn-lg`**: Large buttons (`px-6 py-3 text-lg`)

#### Context-Specific Sizes
- **`barret-action-btn`**: Grid action buttons (`px-2 py-1`)
- **`barret-form-btn`**: Form footer buttons (`px-4 py-2`)
- **`barret-header-btn`**: Page header buttons (`px-6 py-3`)

### Button Group Classes

For consistent spacing between multiple buttons:

- **`barret-btn-group`**: Standard gap (`gap-2`)
- **`barret-btn-group-sm`**: Small gap (`gap-1`)
- **`barret-btn-group-lg`**: Large gap (`gap-3`)

## Usage Patterns

### Form Footer Buttons

```razor
<div class="barret-btn-group justify-end">
    <RadzenButton Text="Cancel"
                  ButtonStyle="ButtonStyle.Secondary"
                  Click="@CancelAsync"
                  class="barret-btn barret-form-btn" />
    <RadzenButton Text="Save"
                  ButtonStyle="ButtonStyle.Primary"
                  Click="@SaveAsync"
                  Disabled="@(!ViewModel.IsValid)"
                  class="barret-btn barret-form-btn" />
</div>
```

### Page Header Buttons

```razor
<div class="barret-btn-group">
    <RadzenButton Text="Add Device"
                  Icon="add"
                  ButtonStyle="ButtonStyle.Primary"
                  Click="@AddDeviceAsync"
                  class="barret-btn barret-header-btn" />
</div>
```

### Grid Action Buttons

```razor
<div class="barret-btn-group-sm">
    <RadzenButton Icon="edit"
                  Variant="Variant.Outlined"
                  ButtonStyle="ButtonStyle.Primary"
                  Size="ButtonSize.Small"
                  Click="@(() => EditDevice(device))"
                  title="Edit Device"
                  class="barret-btn barret-action-btn" />
    <RadzenButton Icon="delete"
                  Variant="Variant.Outlined"
                  ButtonStyle="ButtonStyle.Danger"
                  Size="ButtonSize.Small"
                  Click="@(() => DeleteDevice(device))"
                  title="Delete Device"
                  class="barret-btn barret-action-btn" />
</div>
```

### Small Buttons

```razor
<RadzenButton Text="Quick Action"
              ButtonStyle="ButtonStyle.Light"
              Click="@QuickActionAsync"
              class="barret-btn barret-btn-sm" />
```

### Large Buttons

```razor
<RadzenButton Text="Primary CTA"
              Icon="arrow_forward"
              ButtonStyle="ButtonStyle.Primary"
              Click="@PrimaryActionAsync"
              class="barret-btn barret-btn-lg" />
```

## Button Style Guidelines

### ButtonStyle Usage

- **`ButtonStyle.Primary`**: Main actions (Save, Submit, Add)
- **`ButtonStyle.Secondary`**: Secondary actions (Close, Back)
- **`ButtonStyle.Light`**: Subtle actions (Cancel, Reset)
- **`ButtonStyle.Danger`**: Destructive actions (Delete, Remove)
- **`ButtonStyle.Success`**: Positive confirmations (Confirm, Approve)
- **`ButtonStyle.Warning`**: Caution actions (Warning confirmations)

### Variant Usage

- **Default**: Solid buttons for primary actions
- **`Variant.Outlined`**: Outlined buttons for secondary actions in grids
- **`Variant.Text`**: Text-only buttons for minimal actions

### Size Property vs Classes

- Use Radzen's `Size` property for icon-only buttons
- Use Barret classes for text buttons and consistent spacing
- Combine both when needed for specific use cases

## Migration Examples

### Before (Manual Classes)

```razor
<!-- Inconsistent manual styling -->
<RadzenButton Text="Save" 
              ButtonStyle="ButtonStyle.Primary"
              class="px-4 py-2" />
<RadzenButton Text="Cancel" 
              ButtonStyle="ButtonStyle.Secondary"
              class="px-6 py-3" />
```

### After (Centralized Classes)

```razor
<!-- Consistent centralized styling -->
<div class="barret-btn-group">
    <RadzenButton Text="Cancel"
                  ButtonStyle="ButtonStyle.Secondary"
                  class="barret-btn barret-form-btn" />
    <RadzenButton Text="Save"
                  ButtonStyle="ButtonStyle.Primary"
                  class="barret-btn barret-form-btn" />
</div>
```

## Implementation Checklist

When migrating or creating new buttons:

1. ✅ Add `barret-btn` base class
2. ✅ Choose appropriate size class (`barret-form-btn`, `barret-action-btn`, etc.)
3. ✅ Use `barret-btn-group` for button containers
4. ✅ Select appropriate `ButtonStyle` for the action type
5. ✅ Add `Variant` if using outlined or text buttons
6. ✅ Include proper `Icon` for better UX
7. ✅ Set `Disabled` state based on ViewModel properties
8. ✅ Use ReactiveUI command patterns for click handlers

## CSS Build Process

After making changes to button classes:

1. Run `npm run build:css` to rebuild Tailwind CSS
2. Verify classes are generated in the output CSS
3. Test button styling in the browser

## Future Considerations

This centralized system is designed to:

- Scale to all future Radzen component migrations
- Work seamlessly with MVVM + ReactiveUI patterns
- Maintain consistency across the entire application
- Allow easy global styling changes through Tailwind configuration
- Support responsive design patterns
